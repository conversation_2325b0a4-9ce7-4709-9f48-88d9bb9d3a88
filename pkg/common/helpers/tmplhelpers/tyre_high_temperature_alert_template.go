package tmplhelpers

import (
	"html/template"
)

type TyreHighTemperatureAlertTemplate struct {
	TyreSerialNumber     string
	TyrePosition         int
	AssetIDCredential    string
	CurrentTemperature   float64
	ThresholdTemperature float64
	RedirectLink         template.URL
}

func (t TyreHighTemperatureAlertTemplate) GenerateEmailSubject() string {
	title, _ := ParseStringTemplate("Tyre High Temperature Alert - {{.AssetIDCredential}}", t)
	return title
}

func (t TyreHighTemperatureAlertTemplate) GenerateEmailBody() string {
	body, _ := ParseStringTemplate(`Tyre {{.TyreSerialNumber}} on your vehicle {{.AssetIDCredential}} ((Tyre #{{.TyrePosition}})) has reached a high temperature.
<br>
<br>
<table>
	<tr><td>Current Temperature</td>: <td>{{.CurrentTemperature}} °C</td></tr>
	<tr><td>Temperature Threshold</td>: <td>{{.ThresholdTemperature}} °C</td></tr>
</table><br>

High tyre temperature can indicate a problem, please investigate immediately.

<br><br>
<a href="{{.RedirectLink}}">
<button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">
View Asset
</button>
</a>
<br>`, t)
	return body
}

func (t TyreHighTemperatureAlertTemplate) GeneratePushNotifSubject() string {
	return "Tyre High Temperature Alert"
}

func (t TyreHighTemperatureAlertTemplate) GeneratePushNotifBody() string {
	body, _ := ParseStringTemplate("Tyre {{.TyreSerialNumber}} ( {{.AssetIDCredential}} -  Tyre #{{.TyrePosition}}) temperature is high. Current temperature: {{.CurrentTemperature}}°C, Threshold: {{.ThresholdTemperature}}°C", t)
	return body
}
