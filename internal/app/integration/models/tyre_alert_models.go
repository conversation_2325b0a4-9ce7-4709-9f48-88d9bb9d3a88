package models

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"github.com/jackc/pgtype"
	"github.com/lib/pq"
	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

// TyreAlertConfig represents the ins_tyre_alert_configs table
type TyreAlertConfig struct {
	commonmodel.ModelV2
	ParentAssetID                string         `gorm:"uniqueIndex;not null" json:"parent_asset_id"`
	UsePressureAlert             null.Bool      `gorm:"not null;default:false" json:"use_pressure_alert"`
	UseHighTemperatureAlert      null.Bool      `gorm:"not null;default:false" json:"use_high_temperature_alert"`
	MaxTemperatureThreshold      null.Float     `json:"max_temperature_threshold"`
	UseSendNotification          null.Bool      `gorm:"not null;default:false" json:"use_send_notification"`
	NotificationActionTypes      pq.StringArray `gorm:"type:varchar(255)[]" json:"notification_action_types"`
	NotificationRecipientUserIDs pq.StringArray `gorm:"type:varchar(40)[]" json:"notification_recipient_user_ids"`
	NotifyAssetAssignee          null.Bool      `gorm:"not null;default:false" json:"notify_asset_assignee"`
	UseCreateTickets             null.Bool      `gorm:"not null;default:false" json:"use_create_tickets"`
	TicketAssignedUserID         null.String    `json:"ticket_assigned_user_id"`
}

func (TyreAlertConfig) TableName() string {
	return "ins_tyre_alert_configs"
}

func (tac *TyreAlertConfig) BeforeCreate(db *gorm.DB) error {
	tac.SetUUID("tac")
	tac.ModelV2.BeforeCreate(db)
	return nil
}

func (tac *TyreAlertConfig) BeforeUpdate(db *gorm.DB) error {
	tac.ModelV2.BeforeUpdate(db)
	return nil
}

// TyreAlertType represents the ins_TYRE_ALERT_TYPE table
type TyreAlertType struct {
	commonmodel.ConstantModel
}

func (TyreAlertType) TableName() string {
	return "ins_TYRE_ALERT_TYPE"
}

// TyreAlert represents the ins_tyre_alerts table
type TyreAlert struct {
	commonmodel.ModelV2
	TyreAlertConfigID string       `json:"tyre_alert_config_id"`
	AssetID           string       `gorm:"not null" json:"asset_id"`
	ParentAssetID     string       `gorm:"not null" json:"parent_asset_id"`
	Time              time.Time    `json:"time"`
	TyreAlertTypeCode string       `json:"tyre_alert_type_code"`
	RecordedValue     pgtype.JSONB `gorm:"type:jsonb;default:'{}'" json:"recorded_value"`
	ThresholdValue    pgtype.JSONB `gorm:"type:jsonb;default:'{}'" json:"threshold_value"`
	IsRead            bool         `gorm:"not null;default:false" json:"is_read"`
	ReadDatetime      null.Time    `json:"read_datetime"`
	TicketID          null.String  `json:"ticket_id"`
}

func (TyreAlert) TableName() string {
	return "ins_tyre_alerts"
}

func (ta *TyreAlert) BeforeCreate(db *gorm.DB) error {
	ta.SetUUID("tal")
	ta.ModelV2.BeforeCreate(db)
	return nil
}

func (ta *TyreAlert) BeforeUpdate(db *gorm.DB) error {
	ta.ModelV2.BeforeUpdate(db)
	return nil
}

// Where structs for query filtering
type TyreAlertConfigWhere struct {
	ID             string
	ParentAssetID  string
	ClientID       string
	ClientIDs      []string
	ShowDeleted    bool
	WithOrmDeleted bool
}

type TyreAlertConfigCondition struct {
	Where       TyreAlertConfigWhere
	Preload     TyreAlertConfigPreload
	Columns     []string
	IsForUpdate bool
}

type TyreAlertWhere struct {
	ID                     string
	TyreAlertConfigID      string
	AssetID                string
	ParentAssetID          string
	ClientID               string
	TyreAlertTypeCode      string
	InLastDefaultResetTime bool
	IsRead                 null.Bool
	ShowDeleted            bool
	WithOrmDeleted         bool
}

// Preload structs for eager loading
type TyreAlertConfigPreload struct {
	// Add any related entities if needed in the future
}

type TyreAlertPreload struct {
}

type TyreAlertCondition struct {
	Where       TyreAlertWhere
	Preload     TyreAlertPreload
	Columns     []string
	IsForUpdate bool
}

type GetTyreAlertListParam struct {
	commonmodel.ListRequest
	Cond TyreAlertCondition
}
