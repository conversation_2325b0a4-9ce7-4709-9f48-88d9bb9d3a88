package models

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"github.com/jackc/pgtype"
	"github.com/lib/pq"
	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

// AlertConfigV2 represents the ins_alert_configs_v2 table
type AlertConfigV2 struct {
	commonmodel.ModelV2
	Name                      string         `json:"name"`
	Description               null.String    `json:"description"`
	StatusCode                string         `json:"status_code"`
	ConditionTypeCodes        string         `gorm:"default:AND" json:"condition_type_codes"`
	AssetIDs                  pq.StringArray `gorm:"type:text[]" json:"asset_ids"`
	UseActions                null.Bool      `json:"use_actions"`
	ActionTypeCodes           pq.StringArray `gorm:"type:text[]" json:"action_type_codes"`
	ActionUserRecipients      pq.StringArray `gorm:"type:text[]" json:"action_user_recipients"`
	ActionNotifyAssetAssignee null.Bool      `json:"action_notify_asset_assignee"`
	TicketAutoCreate          null.Bool      `json:"ticket_auto_create"`
	TicketPriorityCode        null.String    `json:"ticket_priority_code"`
	TicketCategoryCode        null.String    `json:"ticket_category_code"`
	TicketDesc                null.String    `gorm:"type:text" json:"ticket_desc"`
	TicketAssignedUserID      null.String    `json:"ticket_assigned_user_id"`
	TicketSubject             string         `json:"ticket_subject" gorm:"default:null"`
	TicketType                string         `json:"ticket_type" gorm:"default:null"`
	DeactivateAfterReach      null.Bool      `json:"deactivate_after_reach"`
	HappenOnAssetIDs          pq.StringArray `gorm:"type:text[]" json:"happen_on_asset_ids"`

	Conditions []AlertConfigV2Condition `gorm:"foreignKey:AlertConfigID" json:"conditions"`
}

// TableName sets the insert table name for this struct type
func (AlertConfigV2) TableName() string {
	return "ins_alert_configs_v2"
}

func (l *AlertConfigV2) BeforeCreate(db *gorm.DB) error {
	l.SetUUID("alc")
	l.ModelV2.BeforeCreate(db)
	return nil
}

func (l *AlertConfigV2) BeforeUpdate(db *gorm.DB) error {
	l.ModelV2.BeforeUpdate(db)
	return nil
}

// AlertConfigCondition represents the ins_alert_config_conditions table
type AlertConfigV2Condition struct {
	commonmodel.ModelV2
	AlertConfigID string                 `json:"alert_config_id"`
	ParameterCode string                 `json:"parameter_code"`
	OperatorCode  string                 `json:"operator_code"`
	Val           pgtype.JSONB           `gorm:"type:jsonb;default:'{}'" json:"val"`
	Parameter     AlertParameter         `gorm:"foreignKey:Code;references:ParameterCode"`
	Operator      AlertConditionOperator `gorm:"foreignKey:Code;references:OperatorCode"`
}

// TableName sets the insert table name for this struct type
func (AlertConfigV2Condition) TableName() string {
	return "ins_alert_config_conditions"
}

func (l *AlertConfigV2Condition) BeforeCreate(db *gorm.DB) error {
	l.SetUUID("acd")
	l.ModelV2.BeforeCreate(db)
	return nil
}

func (l *AlertConfigV2Condition) BeforeUpdate(db *gorm.DB) error {
	l.ModelV2.BeforeUpdate(db)
	return nil
}

// AlertV2 represents the ins_alerts_v2 table
type AlertV2 struct {
	commonmodel.ModelV2
	AlertConfigID     string        `json:"alert_config_id" gorm:"default:null"`
	AssetID           string        `json:"asset_id"`
	ParentAssetID     string        `json:"parent_asset_id"`
	Time              time.Time     `json:"time"`
	RecordedValue     pgtype.JSONB  `gorm:"type:jsonb;default:'{}'" json:"recorded_value"`
	IsRead            bool          `gorm:"default:false;not null" json:"is_read"`
	ReadDatetime      null.Time     `json:"read_datetime"`
	TicketID          string        `json:"ticket_id"`
	TicketType        string        `json:"ticket_type" gorm:"default:null"`
	AssetCategoryCode string        `json:"asset_category_code"`
	TyreAlertID       null.String   `json:"tyre_alert_id"`
	AlertConfig       AlertConfigV2 `gorm:"foreignKey:AlertConfigID" json:"alert_config"`
	TyreAlert         TyreAlert     `gorm:"foreignKey:TyreAlertID" json:"tyre_alert"`
}

// TableName sets the insert table name for this struct type
func (AlertV2) TableName() string {
	return "ins_alerts_v2"
}

func (l *AlertV2) BeforeCreate(db *gorm.DB) error {
	l.SetUUID("alr")
	l.ModelV2.BeforeCreate(db)
	return nil
}

func (l *AlertV2) BeforeUpdate(db *gorm.DB) error {
	l.ModelV2.BeforeUpdate(db)
	return nil
}

type AlertConfigV2Where struct {
	ID                 string
	AssetIDs           []string
	NotHappenOnAssetID string
	ClientID           string
	StatusCode         string
	WithOrmDeleted     bool
}

type AlertConfigV2Preload struct {
	Conditions          bool
	ConditionsParameter bool
	ConditionsOperator  bool
}

type AlertConfigV2Cond struct {
	Where       AlertConfigV2Where
	Preload     AlertConfigV2Preload
	Columns     []string
	IsForUpdate bool
}

type GetAlertConfigV2ListParam struct {
	commonmodel.ListRequest
	Cond AlertConfigV2Cond
}

type AlertV2Where struct {
	ID                      string
	AssetID                 string
	AssetIDs                []string
	AlertConfigID           string
	InLastDefaultResetTime  bool
	IsRead                  null.Bool
	ClientID                string
	StatusCode              string
	WithOrmDeleted          bool
	AssetCategories         []string
	ExcludedAssetCategories []string
}

type AlertV2Preload struct {
	AlertConfig bool
	TyreAlert   bool
}

type AlertV2Cond struct {
	Where       AlertV2Where
	Preload     AlertV2Preload
	Columns     []string
	IsForUpdate bool
}

type GetAlertV2ListParam struct {
	commonmodel.ListRequest
	Cond AlertV2Cond
}
