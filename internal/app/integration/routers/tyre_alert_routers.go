package routers

import (
	"assetfindr/internal/app/integration/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterTyreAlertRoutes(route *gin.Engine, tyreAlertHandler *handler.TyreAlertHandler) *gin.Engine {

	tyreAlertRoutes := route.Group("/v1/tyre-alerts", middleware.TokenValidationMiddleware())
	{
		tyreAlertConfigRoutes := tyreAlertRoutes.Group("/configs/parent-asset")
		{
			tyreAlertConfigRoutes.GET("/:parent_asset_id", tyreAlertHandler.GetTyreAlertConfigByParentAssetID)
			tyreAlertConfigRoutes.POST("/:parent_asset_id", tyreAlertHandler.UpsertTyreAlertConfigByParentAssetID)
		}

		tyreAlertListRoutes := tyreAlertRoutes.Group("/parent-asset")
		{
			tyreAlertListRoutes.GET("/:parent_asset_id", tyreAlertHandler.GetTyreAlertsByParentAssetID)
			tyreAlertListRoutes.PUT("/:parent_asset_id/read-all", tyreAlertHandler.MarkAllTyreAlertsAsReadByParentAssetID)
		}

		tyreAlertRoutes.PATCH("/:id/read", tyreAlertHandler.SetTyreAlertAsRead)
	}

	return route
}
