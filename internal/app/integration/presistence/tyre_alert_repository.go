package presistence

import (
	"assetfindr/internal/app/integration/models"
	"assetfindr/internal/app/integration/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type tyreAlertRepository struct{}

func NewTyreAlertRepository() repository.TyreAlertRepository {
	return &tyreAlertRepository{}
}

// TyreAlertConfig operations

func (r *tyreAlertRepository) GetTyreAlertConfig(ctx context.Context, dB database.DBI, cond models.TyreAlertConfigCondition) (*models.TyreAlertConfig, error) {
	var config models.TyreAlertConfig
	query := dB.GetTx().Model(&config)

	enrichTyreAlertConfigQueryWithWhere(query, cond.Where)
	enrichTyreAlertConfigQueryWithPreload(query, cond.Preload)

	err := query.First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("TYRE_ALERT_CONFIG")
		}
		return nil, err
	}

	return &config, nil
}

func (r *tyreAlertRepository) UpsertTyreAlertConfig(ctx context.Context, dB database.DBI, config *models.TyreAlertConfig) error {
	return dB.GetTx().Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "parent_asset_id"}},
		UpdateAll: true,
	}).Create(config).Error
}

// TyreAlert operations

func (r *tyreAlertRepository) GetTyreAlertList(ctx context.Context, dB database.DBI, param models.GetTyreAlertListParam) (int, []models.TyreAlert, error) {
	var totalRecords int64
	var alerts []models.TyreAlert
	query := dB.GetTx().Model(&alerts)

	enrichTyreAlertQueryWithWhere(query, param.Cond.Where)
	enrichTyreAlertQueryWithPreload(query, param.Cond.Preload)

	// Count total records
	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	// Apply pagination
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Order("time DESC").Offset(offset).Limit(param.PageSize).Find(&alerts).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), alerts, nil
}

func (r *tyreAlertRepository) GetTyreAlert(ctx context.Context, dB database.DBI, cond models.TyreAlertCondition) (*models.TyreAlert, error) {
	var alert models.TyreAlert
	query := dB.GetTx().Model(&alert)

	enrichTyreAlertQueryWithWhere(query, cond.Where)
	enrichTyreAlertQueryWithPreload(query, cond.Preload)

	err := query.First(&alert).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("TYRE_ALERT")
		}
		return nil, err
	}

	return &alert, nil
}

func (r *tyreAlertRepository) CreateTyreAlert(ctx context.Context, dB database.DBI, alert *models.TyreAlert) error {
	return dB.GetTx().Create(alert).Error
}

// Helper functions for query enrichment

func enrichTyreAlertConfigQueryWithWhere(query *gorm.DB, where models.TyreAlertConfigWhere) {
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	}
	if where.ParentAssetID != "" {
		query.Where("parent_asset_id = ?", where.ParentAssetID)
	}

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	}
	if len(where.ClientIDs) > 0 {
		query.Where("client_id IN ?", where.ClientIDs)
	}
	if !where.ShowDeleted {
		query.Where("deleted_at IS NULL")
	}
	if where.WithOrmDeleted {
		query.Unscoped()
	}
}

func enrichTyreAlertConfigQueryWithPreload(query *gorm.DB, preload models.TyreAlertConfigPreload) {
	// Add preload logic if needed in the future
}

func enrichTyreAlertQueryWithWhere(query *gorm.DB, where models.TyreAlertWhere) {
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	}
	if where.TyreAlertConfigID != "" {
		query.Where("tyre_alert_config_id = ?", where.TyreAlertConfigID)
	}

	if where.AssetID != "" {
		query.Where("asset_id = ?", where.AssetID)
	}

	if where.ParentAssetID != "" {
		query.Where("parent_asset_id = ?", where.ParentAssetID)
	}
	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	}

	if where.TyreAlertTypeCode != "" {
		query.Where("tyre_alert_type_code = ?", where.TyreAlertTypeCode)
	}

	if where.InLastDefaultResetTime {
		query.Where("time > NOW() - INTERVAL '?'", gorm.Expr("1 days"))
	}

	if where.IsRead.Valid {
		query.Where("is_read = ?", where.IsRead.Bool)
	}

	if !where.ShowDeleted {
		query.Where("deleted_at IS NULL")
	}
	if where.WithOrmDeleted {
		query.Unscoped()
	}
}

func enrichTyreAlertQueryWithPreload(query *gorm.DB, preload models.TyreAlertPreload) {

}
