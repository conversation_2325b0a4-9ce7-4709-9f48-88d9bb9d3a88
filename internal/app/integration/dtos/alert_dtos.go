package dtos

import (
	"assetfindr/internal/app/integration/models"
	"assetfindr/pkg/common/commonmodel"
	"time"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

type AlertListReq struct {
	commonmodel.ListRequest
	StateCategoryCode       string   `form:"state_category_code"`
	TriggerCode             string   `form:"trigger_code"`
	AssetID                 string   `form:"asset_id"`
	AssetIDs                []string `form:"asset_ids"`
	AlertConfigCategoryCode string   `form:"alert_config_category_code"`
}

type Alert struct {
	ID                        string                    `json:"id"`
	AlertConfigTriggerStateID string                    `json:"alert_config_trigger_state_id"`
	AlertConfigTriggerID      string                    `json:"alert_config_trigger_id"`
	AlertConfigID             string                    `json:"alert_config_id"`
	StateCategoryCode         string                    `json:"state_category_code"`
	TriggerCode               string                    `json:"trigger_code"`
	Time                      time.Time                 `json:"time"`
	RecordedValue             float64                   `json:"recorded_value"`
	AssetID                   string                    `json:"asset_id"`
	TicketID                  string                    `json:"ticket_id"`
	ReadDatetime              *time.Time                `json:"read_datetime"`
	IsRead                    bool                      `json:"is_read"`
	AssetReferenceNumber      string                    `json:"asset_reference_number"`
	AssetSerialNumber         string                    `json:"asset_serial_number"`
	TicketStatusCode          string                    `json:"ticket_status_code"`
	TicketStatus              commonmodel.ConstantModel `json:"ticket_status"`
	AlertConfig               AlertConfig               `json:"alert_config"`
	StateCategory             commonmodel.ConstantModel `json:"state_category"`
	CreatedAt                 time.Time                 `json:"created_at"`
}

type AlertParameterListReq struct {
	commonmodel.ListRequest
	SourceCodes []string `form:"source_codes"`
}

type AlertConditionOperatorListReq struct {
	commonmodel.ListRequest
	DataTypes []string `form:"data_types"`
}

type AlertV2ListReq struct {
	commonmodel.ListRequest
	AssetIDs []string `form:"asset_ids"`
}

type AlertV2 struct {
	ID            string         `json:"id"`
	AlertConfigID string         `json:"alert_config_id"`
	AssetID       string         `json:"asset_id"`
	Time          time.Time      `json:"time"`
	RecordedValue pgtype.JSONB   `gorm:"type:jsonb;default:'{}'" json:"recorded_value"`
	IsRead        bool           `gorm:"default:false;not null" json:"is_read"`
	ReadDatetime  null.Time      `json:"read_datetime"`
	TicketID      string         `json:"ticket_id"`
	TicketType    string         `json:"ticket_type"`
	AlertConfig   AlertConfigV2  `gorm:"foreignKey:AlertConfigID" json:"alert_config"`
	TyreAlert     *TyreAlertResp `gorm:"foreignKey:TyreAlertID" json:"tyre_alert"`
}

func BuildAlertV2Resp(alert models.AlertV2) AlertV2 {
	resp := AlertV2{
		ID:            alert.ID,
		AlertConfigID: alert.AlertConfigID,
		AssetID:       alert.AssetID,
		Time:          alert.Time,
		RecordedValue: alert.RecordedValue,
		IsRead:        alert.IsRead,
		ReadDatetime:  alert.ReadDatetime,
		TicketID:      alert.TicketID,
		TicketType:    alert.TicketType,
		AlertConfig:   BuildAlertConfigV2Resp(alert.AlertConfig),
	}

	if alert.TyreAlertID.Valid {
		temp := BuildTyreAlertResp(alert.TyreAlert)
		resp.TyreAlert = &temp

		resp.AlertConfig.Name = alert.TyreAlert.Title
		resp.AlertConfig.Description = null.StringFrom(alert.TyreAlert.Description)
	}

	return resp
}
