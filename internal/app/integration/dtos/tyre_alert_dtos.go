package dtos

import (
	"assetfindr/internal/app/integration/models"
	"assetfindr/pkg/common/commonmodel"
	"time"

	"github.com/jackc/pgtype"
	"github.com/lib/pq"
	"gopkg.in/guregu/null.v4"
)

type TyreAlertConfigReq struct {
	UsePressureAlert             null.Bool      `json:"use_pressure_alert"`
	UseHighTemperatureAlert      null.Bool      `json:"use_high_temperature_alert"`
	MaxTemperatureThreshold      null.Float     `json:"max_temperature_threshold"`
	UseSendNotification          null.Bool      `json:"use_send_notification"`
	NotificationActionTypes      pq.StringArray `json:"notification_action_types"`
	NotificationRecipientUserIDs pq.StringArray `json:"notification_recipient_user_ids"`
	NotifyAssetAssignee          null.Bool      `json:"notify_asset_assignee"`
	UseCreateTickets             null.Bool      `json:"use_create_tickets"`
	TicketAssignedUserID         null.String    `json:"ticket_assigned_user_id"`
}

type TyreAlertConfigResp struct {
	ID                           string         `json:"id"`
	ParentAssetID                string         `json:"parent_asset_id"`
	UsePressureAlert             null.Bool      `json:"use_pressure_alert"`
	UseHighTemperatureAlert      null.Bool      `json:"use_high_temperature_alert"`
	MaxTemperatureThreshold      null.Float     `json:"max_temperature_threshold"`
	UseSendNotification          null.Bool      `json:"use_send_notification"`
	NotificationActionTypes      pq.StringArray `json:"notification_action_types"`
	NotificationRecipientUserIDs pq.StringArray `json:"notification_recipient_user_ids"`
	NotificationRecipientUsers   []User         `json:"notification_recipient_users"`
	NotifyAssetAssignee          null.Bool      `json:"notify_asset_assignee"`
	UseCreateTickets             null.Bool      `json:"use_create_tickets"`
	TicketAssignedUserID         null.String    `json:"ticket_assigned_user_id"`
	TicketAssignedUserName       null.String    `json:"ticket_assigned_user_name"`
	CreatedAt                    time.Time      `json:"created_at"`
	UpdatedAt                    time.Time      `json:"updated_at"`
	CreatedBy                    string         `json:"created_by"`
	UpdatedBy                    string         `json:"updated_by"`
	ClientID                     string         `json:"client_id"`
}

// TyreAlertListReq represents the request parameters for listing tyre alerts
type TyreAlertListReq struct {
	commonmodel.ListRequest
}

// TyreAlertResp represents the response for a single tyre alert
type TyreAlertResp struct {
	ID                string               `json:"id"`
	Title             string               `json:"title"`
	Description       string               `json:"description"`
	TyreAlertConfigID string               `json:"tyre_alert_config_id"`
	AssetID           string               `json:"asset_id"`
	ParentAssetID     string               `json:"parent_asset_id"`
	Time              time.Time            `json:"time"`
	TyreAlertTypeCode string               `json:"tyre_alert_type_code"`
	TyreAlertType     models.TyreAlertType `json:"tyre_alert_type"`
	RecordedValue     pgtype.JSONB         `json:"recorded_value"`
	ThresholdValue    pgtype.JSONB         `json:"threshold_value"`
	IsRead            bool                 `json:"is_read"`
	ReadDatetime      null.Time            `json:"read_datetime"`
	TicketID          null.String          `json:"ticket_id"`
	CreatedAt         time.Time            `json:"created_at"`
	UpdatedAt         time.Time            `json:"updated_at"`
}

// BuildTyreAlertConfigResp builds response DTO from model
func BuildTyreAlertConfigResp(config models.TyreAlertConfig, userMapNames map[string]string) TyreAlertConfigResp {
	ticketAssignedUserName := null.String{}
	if config.TicketAssignedUserID.String != "" {
		ticketAssignedUserName = null.StringFrom(userMapNames[config.TicketAssignedUserID.String])
	}

	users := []User{}
	for _, userID := range config.NotificationRecipientUserIDs {
		users = append(users, User{
			ID:       userID,
			FullName: userMapNames[userID],
		})
	}

	return TyreAlertConfigResp{
		ID:                           config.ID,
		ParentAssetID:                config.ParentAssetID,
		UsePressureAlert:             config.UsePressureAlert,
		UseHighTemperatureAlert:      config.UseHighTemperatureAlert,
		MaxTemperatureThreshold:      config.MaxTemperatureThreshold,
		UseSendNotification:          config.UseSendNotification,
		NotificationActionTypes:      config.NotificationActionTypes,
		NotificationRecipientUserIDs: config.NotificationRecipientUserIDs,
		NotificationRecipientUsers:   users,
		NotifyAssetAssignee:          config.NotifyAssetAssignee,
		UseCreateTickets:             config.UseCreateTickets,
		TicketAssignedUserID:         config.TicketAssignedUserID,
		TicketAssignedUserName:       ticketAssignedUserName,
		CreatedAt:                    config.CreatedAt,
		UpdatedAt:                    config.UpdatedAt,
		CreatedBy:                    config.CreatedBy,
		UpdatedBy:                    config.UpdatedBy,
		ClientID:                     config.ClientID,
	}
}

func BuildTyreAlertResp(alert models.TyreAlert) TyreAlertResp {
	resp := TyreAlertResp{
		ID:                alert.ID,
		Title:             alert.Title,
		Description:       alert.Description,
		TyreAlertConfigID: alert.TyreAlertConfigID,
		AssetID:           alert.AssetID,
		ParentAssetID:     alert.ParentAssetID,
		Time:              alert.Time,
		TyreAlertTypeCode: alert.TyreAlertTypeCode,
		TyreAlertType:     alert.TyreAlertType,
		RecordedValue:     alert.RecordedValue,
		ThresholdValue:    alert.ThresholdValue,
		IsRead:            alert.IsRead,
		ReadDatetime:      alert.ReadDatetime,
		TicketID:          alert.TicketID,
		CreatedAt:         alert.CreatedAt,
		UpdatedAt:         alert.UpdatedAt,
	}

	return resp
}

func BuildTyreAlertListResp(alerts []models.TyreAlert) []TyreAlertResp {
	alertResps := make([]TyreAlertResp, len(alerts))
	for i, alert := range alerts {
		alertResps[i] = BuildTyreAlertResp(alert)
	}

	return alertResps
}
