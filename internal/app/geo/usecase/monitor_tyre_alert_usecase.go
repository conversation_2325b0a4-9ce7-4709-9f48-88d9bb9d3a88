package usecase

import (
	assetModel "assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/geo/models"
	integrationConstants "assetfindr/internal/app/integration/constants"
	integrationModel "assetfindr/internal/app/integration/models"
	notificationConstants "assetfindr/internal/app/notification/constants"
	notificationDtos "assetfindr/internal/app/notification/dtos"
	userModels "assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/contexthelpers"
	"assetfindr/pkg/common/helpers/tmplhelpers"
	"html/template"

	"context"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

func (uc *TrackingUseCase) MonitorTyreAlertV2(ctx context.Context, tyreSensorData *models.TyreSensor, assetVehicle *assetModel.AssetVehicle, axleConfigs []assetModel.AxleConfiguration) {
	if !tyreSensorData.ParentAssetID.Valid {
		return
	}

	tyreAlertConfig, err := uc.tyreAlertRepo.GetTyreAlertConfig(ctx, uc.DB.DB(), integrationModel.TyreAlertConfigCondition{
		Where: integrationModel.TyreAlertConfigWhere{
			ParentAssetID: tyreSensorData.ParentAssetID.String,
		},
	})
	if err != nil {
		if !errorhandler.IsErrNotFound(err) {
			commonlogger.Warnf("failed to get tyre alert config, err: %v", err)
			return
		}
		return
	}

	uc.MonitorTyreAlertPressure(ctx, tyreSensorData, tyreAlertConfig, assetVehicle, axleConfigs)
	uc.MonitorTyreAlertHighTemperature(ctx, tyreSensorData, tyreAlertConfig)
}

const (
	labelPressure       = "tyre.pressure"
	labelPressureMax    = "pressure_max"
	labelPressureMin    = "pressure_min"
	labelTemperature    = "tyre.temperature"
	labelTemperatureMax = "temperature_max"
)

func (uc *TrackingUseCase) MonitorTyreAlertPressure(ctx context.Context, tyreSensorData *models.TyreSensor, tyreAlertConfig *integrationModel.TyreAlertConfig, assetVehicle *assetModel.AssetVehicle, axleConfigs []assetModel.AxleConfiguration) {
	if !tyreAlertConfig.UsePressureAlert.Bool {
		return
	}

	if !tyreSensorData.Pressure.Valid {
		return
	}

	mapPositionToPressureFromAxle := assetModel.MapPositionToPressureFromAxle(axleConfigs)
	pressureConfig := mapPositionToPressureFromAxle[int(tyreSensorData.TyrePosition.Int64)]
	if !pressureConfig.PressureMin.Valid && !pressureConfig.PressureMax.Valid {
		return
	}

	recordedValue := pgtype.JSONB{}
	recordedValue.Set(map[string]interface{}{
		labelPressure: tyreSensorData.Pressure,
	})

	if pressureConfig.PressureMax.Valid && tyreSensorData.Pressure.Float64 > pressureConfig.PressureMax.Float64 {
		_, err := uc.tyreAlertRepo.GetTyreAlert(ctx, uc.DB.DB(), integrationModel.TyreAlertCondition{
			Where: integrationModel.TyreAlertWhere{
				ParentAssetID:          tyreSensorData.ParentAssetID.String,
				TyreAlertTypeCode:      integrationConstants.TYRE_ALERT_TYPE_PRESSURE_OVERINFLATED,
				InLastDefaultResetTime: true,
				IsRead:                 null.BoolFrom(false),
			},
		})
		if err == nil {
			return
		}

		if !errorhandler.IsErrNotFound(err) {
			commonlogger.Errorf("failed to get tyre alert, err: %v", err)
			return
		}

		tyreSensorData.TyreAlertTypes = append(tyreSensorData.TyreAlertTypes, integrationConstants.TYRE_ALERT_TYPE_PRESSURE_OVERINFLATED)
		thresholdValue := pgtype.JSONB{}
		thresholdValue.Set(map[string]interface{}{
			labelPressureMax: pressureConfig.PressureMax,
		})
		tyreAlert := &integrationModel.TyreAlert{
			ModelV2: commonmodel.ModelV2{
				CreatedBy: "SYSTEM",
				UpdatedBy: "SYSTEM",
				ClientID:  tyreSensorData.ClientID,
			},
			TyreAlertConfigID: tyreAlertConfig.ID,
			AssetID:           tyreSensorData.AssetID,
			ParentAssetID:     tyreSensorData.ParentAssetID.String,
			Time:              tyreSensorData.Time,
			TyreAlertTypeCode: integrationConstants.TYRE_ALERT_TYPE_PRESSURE_OVERINFLATED,
			RecordedValue:     recordedValue,
			ThresholdValue:    thresholdValue,
		}
		err = uc.tyreAlertRepo.CreateTyreAlert(ctx, uc.DB.DB(), tyreAlert)
		if err != nil {
			commonlogger.Errorf("failed to create tyre alert, err: %v", err)
			return
		}

		if tyreAlertConfig.UseSendNotification.Bool {
			go uc.sendTyreOverinflatedAlertNotification(contexthelpers.WithoutCancel(ctx), tyreSensorData, tyreAlertConfig,
				tyreSensorData.Pressure,
				pressureConfig.PressureMax,
			)
		}
	}

	if pressureConfig.PressureMin.Valid && tyreSensorData.Pressure.Float64 < pressureConfig.PressureMin.Float64 {
		_, err := uc.tyreAlertRepo.GetTyreAlert(ctx, uc.DB.DB(), integrationModel.TyreAlertCondition{
			Where: integrationModel.TyreAlertWhere{
				ParentAssetID:          tyreSensorData.ParentAssetID.String,
				TyreAlertTypeCode:      integrationConstants.TYRE_ALERT_TYPE_PRESSURE_UNDERINFLATED,
				InLastDefaultResetTime: true,
				IsRead:                 null.BoolFrom(false),
			},
		})
		if err == nil {
			return
		}

		if !errorhandler.IsErrNotFound(err) {
			commonlogger.Errorf("failed to get tyre alert, err: %v", err)
			return
		}

		tyreSensorData.TyreAlertTypes = append(tyreSensorData.TyreAlertTypes, integrationConstants.TYRE_ALERT_TYPE_PRESSURE_UNDERINFLATED)
		thresholdValue := pgtype.JSONB{}
		thresholdValue.Set(map[string]interface{}{
			labelPressureMin: pressureConfig.PressureMin,
		})
		tyreAlert := &integrationModel.TyreAlert{
			ModelV2: commonmodel.ModelV2{
				CreatedBy: "SYSTEM",
				UpdatedBy: "SYSTEM",
				ClientID:  tyreSensorData.ClientID,
			},
			TyreAlertConfigID: tyreAlertConfig.ID,
			AssetID:           tyreSensorData.AssetID,
			ParentAssetID:     tyreSensorData.ParentAssetID.String,
			Time:              tyreSensorData.Time,
			TyreAlertTypeCode: integrationConstants.TYRE_ALERT_TYPE_PRESSURE_UNDERINFLATED,
			RecordedValue:     recordedValue,
			ThresholdValue:    thresholdValue,
		}
		err = uc.tyreAlertRepo.CreateTyreAlert(ctx, uc.DB.DB(), tyreAlert)
		if err != nil {
			commonlogger.Errorf("failed to create tyre alert, err: %v", err)
			return
		}

		if tyreAlertConfig.UseSendNotification.Bool {
			go uc.sendTyreUnderinflatedAlertNotification(contexthelpers.WithoutCancel(ctx), tyreSensorData, tyreAlertConfig,
				tyreSensorData.Pressure,
				pressureConfig.PressureMin,
			)
		}
	}
}

func (uc *TrackingUseCase) MonitorTyreAlertHighTemperature(ctx context.Context, tyreSensorData *models.TyreSensor, tyreAlertConfig *integrationModel.TyreAlertConfig) {
	if !tyreAlertConfig.UseHighTemperatureAlert.Bool {
		return
	}

	if !tyreSensorData.Temperature.Valid {
		return
	}

	if !tyreAlertConfig.MaxTemperatureThreshold.Valid {
		return
	}

	if tyreSensorData.Temperature.Float64 > tyreAlertConfig.MaxTemperatureThreshold.Float64 {
		_, err := uc.tyreAlertRepo.GetTyreAlert(ctx, uc.DB.DB(), integrationModel.TyreAlertCondition{
			Where: integrationModel.TyreAlertWhere{
				ParentAssetID:          tyreSensorData.ParentAssetID.String,
				TyreAlertTypeCode:      integrationConstants.TYRE_ALERT_TYPE_HIGH_TEMPERATURE,
				InLastDefaultResetTime: true,
				IsRead:                 null.BoolFrom(false),
			},
		})
		if err == nil {
			return
		}

		if !errorhandler.IsErrNotFound(err) {
			commonlogger.Errorf("failed to get tyre alert, err: %v", err)
			return
		}

		tyreSensorData.TyreAlertTypes = append(tyreSensorData.TyreAlertTypes, integrationConstants.TYRE_ALERT_TYPE_HIGH_TEMPERATURE)
		recordedValue := pgtype.JSONB{}
		recordedValue.Set(map[string]interface{}{
			labelTemperature: tyreSensorData.Temperature,
		})

		thresholdValue := pgtype.JSONB{}
		thresholdValue.Set(map[string]interface{}{
			labelTemperatureMax: tyreAlertConfig.MaxTemperatureThreshold,
		})
		tyreAlert := &integrationModel.TyreAlert{
			ModelV2: commonmodel.ModelV2{
				CreatedBy: "SYSTEM",
				UpdatedBy: "SYSTEM",
				ClientID:  tyreSensorData.ClientID,
			},
			TyreAlertConfigID: tyreAlertConfig.ID,
			AssetID:           tyreSensorData.AssetID,
			ParentAssetID:     tyreSensorData.ParentAssetID.String,
			Time:              tyreSensorData.Time,
			TyreAlertTypeCode: integrationConstants.TYRE_ALERT_TYPE_HIGH_TEMPERATURE,
			RecordedValue:     recordedValue,
			ThresholdValue:    thresholdValue,
		}
		err = uc.tyreAlertRepo.CreateTyreAlert(ctx, uc.DB.DB(), tyreAlert)
		if err != nil {
			commonlogger.Errorf("failed to create tyre alert, err: %v", err)
			return
		}

		if tyreAlertConfig.UseSendNotification.Bool {
			go uc.sendTyreHighTemperatureAlertNotification(contexthelpers.WithoutCancel(ctx), tyreSensorData, tyreAlertConfig,
				tyreSensorData.Temperature,
			)
		}
	}
}

func (uc *TrackingUseCase) sendTyreOverinflatedAlertNotification(
	ctx context.Context,
	tyreSensorData *models.TyreSensor,
	tyreAlertConfig *integrationModel.TyreAlertConfig,
	currentPressure null.Float,
	pressureMax null.Float,
) {

	asset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			ID: tyreSensorData.AssetID,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get asset for tyre overinflated alert notification, err: %v", err)
		return
	}

	parentAsset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			ID: tyreSensorData.ParentAssetID.String,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get parent asset for tyre overinflated alert notification, err: %v", err)
		return
	}

	client, err := uc.userRepo.GetClient(ctx, uc.DB.DB(), userModels.ClientCondition{
		Where: userModels.ClientWhere{
			ID: asset.ClientID,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get client for tyre overinflated alert notification, err: %v", err)
		return
	}

	targetURL := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notificationConstants.DESTINATION_TYPE_ASSET_ALERT_TYRE, tyreSensorData.ParentAssetID.String)

	templ := tmplhelpers.TyreOverinflatedAlertTemplate{
		TyreSerialNumber:     asset.SerialNumber,
		TyrePosition:         int(tyreSensorData.TyrePosition.Int64),
		AssetIDCredential:    parentAsset.GetAssetIdentForNotif(),
		CurrentPressure:      currentPressure.Float64,
		ThresholdMaxPressure: pressureMax.Float64,
		RedirectLink:         template.URL(targetURL),
	}

	uc.sendTyreAlertNotificationCommon(ctx, tyreSensorData, tyreAlertConfig, asset,
		templ.GenerateEmailSubject(), templ.GenerateEmailBody(),
		templ.GeneratePushNotifSubject(), templ.GeneratePushNotifBody(), targetURL)
}

func (uc *TrackingUseCase) sendTyreUnderinflatedAlertNotification(
	ctx context.Context,
	tyreSensorData *models.TyreSensor,
	tyreAlertConfig *integrationModel.TyreAlertConfig,
	currentPressure null.Float,
	thresholdMinPressure null.Float,
) {

	asset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			ID: tyreSensorData.AssetID,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get asset for tyre underinflated alert notification, err: %v", err)
		return
	}

	parentAsset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			ID: tyreSensorData.ParentAssetID.String,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get parent asset for tyre underinflated alert notification, err: %v", err)
		return
	}

	client, err := uc.userRepo.GetClient(ctx, uc.DB.DB(), userModels.ClientCondition{
		Where: userModels.ClientWhere{
			ID: asset.ClientID,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get client for tyre underinflated alert notification, err: %v", err)
		return
	}

	targetURL := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notificationConstants.DESTINATION_TYPE_ASSET_ALERT_TYRE, tyreSensorData.ParentAssetID.String)

	templ := tmplhelpers.TyreUnderinflatedAlertTemplate{
		TyreSerialNumber:     asset.SerialNumber,
		TyrePosition:         int(tyreSensorData.TyrePosition.Int64),
		AssetIDCredential:    parentAsset.GetAssetIdentForNotif(),
		CurrentPressure:      currentPressure.Float64,
		ThresholdMinPressure: thresholdMinPressure.Float64,
		RedirectLink:         template.URL(targetURL),
	}

	uc.sendTyreAlertNotificationCommon(ctx, tyreSensorData, tyreAlertConfig, asset,
		templ.GenerateEmailSubject(), templ.GenerateEmailBody(),
		templ.GeneratePushNotifSubject(), templ.GeneratePushNotifBody(), targetURL)
}

func (uc *TrackingUseCase) sendTyreHighTemperatureAlertNotification(
	ctx context.Context,
	tyreSensorData *models.TyreSensor,
	tyreAlertConfig *integrationModel.TyreAlertConfig,
	currentTemperature null.Float,
) {

	asset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			ID: tyreSensorData.AssetID,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get asset for tyre high temperature alert notification, err: %v", err)
		return
	}

	parentAsset, err := uc.assetRepo.GetAsset(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			ID: tyreSensorData.ParentAssetID.String,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get parent asset for tyre high temperature alert notification, err: %v", err)
		return
	}

	client, err := uc.userRepo.GetClient(ctx, uc.DB.DB(), userModels.ClientCondition{
		Where: userModels.ClientWhere{
			ID: asset.ClientID,
		},
	})
	if err != nil {
		commonlogger.Errorf("failed to get client for tyre high temperature alert notification, err: %v", err)
		return
	}

	targetURL := uc.notifUseCase.GenerateTargetURL(ctx, client.ClientAlias, notificationConstants.DESTINATION_TYPE_ASSET_ALERT_TYRE, tyreSensorData.ParentAssetID.String)

	templ := tmplhelpers.TyreHighTemperatureAlertTemplate{
		TyreSerialNumber:     asset.SerialNumber,
		TyrePosition:         int(tyreSensorData.TyrePosition.Int64),
		AssetIDCredential:    parentAsset.GetAssetIdentForNotif(),
		CurrentTemperature:   currentTemperature.Float64,
		ThresholdTemperature: tyreAlertConfig.MaxTemperatureThreshold.Float64,
		RedirectLink:         template.URL(targetURL),
	}

	uc.sendTyreAlertNotificationCommon(ctx, tyreSensorData, tyreAlertConfig, asset,
		templ.GenerateEmailSubject(), templ.GenerateEmailBody(),
		templ.GeneratePushNotifSubject(), templ.GeneratePushNotifBody(), targetURL)
}

func (uc *TrackingUseCase) sendTyreAlertNotificationCommon(
	ctx context.Context,
	tyreSensorData *models.TyreSensor,
	tyreAlertConfig *integrationModel.TyreAlertConfig,
	asset *assetModel.Asset,
	emailSubject string,
	emailBody string,
	pushSubject string,
	pushBody string,
	targetURL string,
) {

	userIDs := make([]string, 0)

	for _, userID := range tyreAlertConfig.NotificationRecipientUserIDs {
		userIDs = append(userIDs, userID)
	}

	if tyreAlertConfig.NotifyAssetAssignee.Bool {
		assetAssignment, err := uc.assetAssignmentRepo.GetAssetAssignment(ctx, uc.DB.DB(), assetModel.AssetAssignmentCondition{
			Where: assetModel.AssetAssignmentWhere{
				AssetID: tyreSensorData.ParentAssetID.String,
			},
		})
		if err == nil && assetAssignment.UserID != "" {
			userIDs = append(userIDs, assetAssignment.UserID)
		}
	}

	if len(userIDs) == 0 {
		commonlogger.Warnf("no recipients configured for tyre alert notification")
		return
	}

	notifItems := make([]notificationDtos.CreateNotificationItem, 0, len(userIDs))
	for _, userID := range userIDs {
		notifItem := notificationDtos.CreateNotificationItem{
			UserID:            userID,
			SourceCode:        notificationConstants.NOTIFICATION_SOURCE_CODE_TYRE_ALERT,
			SourceReferenceID: tyreSensorData.AssetID,
			TargetReferenceID: tyreSensorData.ParentAssetID.String,
			TargetURL:         targetURL,
			MessageHeader:     emailSubject,
			MessageBody:       emailBody,
			MessageFirebase: notificationDtos.MessageFirebase{
				Title: pushSubject,
				Body:  pushBody,
			},
			ClientID:        asset.ClientID,
			TypeCode:        notificationConstants.NOTIFICATION_TYPE_ALERT_CODE,
			ContentTypeCode: notificationConstants.NOTIF_CONTENT_TYPE_CODE_ALERT,
			ReferenceCode:   notificationConstants.NOTIF_REF_ASSET_ALERT,
			ReferenceValue:  tyreSensorData.AssetID,
		}
		notifItems = append(notifItems, notifItem)
	}

	sendToEmail := false
	sendToPushNotif := false
	for _, actionType := range tyreAlertConfig.NotificationActionTypes {
		switch actionType {
		case integrationConstants.ALERT_ACTION_TYPE_CODE_EMAIL_NOTIFICATION:
			sendToEmail = true
		case integrationConstants.ALERT_ACTION_TYPE_CODE_WEB_NOTIFICATION,
			integrationConstants.ALERT_ACTION_TYPE_CODE_MOBILE_APP_NOTIFICATION,
			integrationConstants.ALERT_ACTION_TYPE_CODE_WEB_MOBILE_APP_NOTIFICATION:
			sendToPushNotif = true
		}
	}

	createNotifReq := notificationDtos.CreateNotificationReq{
		Items:           notifItems,
		SendToEmail:     sendToEmail,
		SendToPushNotif: sendToPushNotif,
	}

	err := uc.notifUseCase.CreateNotification(ctx, createNotifReq)
	if err != nil {
		commonlogger.Errorf("failed to send tyre alert notification, err: %v", err)
	}
}
